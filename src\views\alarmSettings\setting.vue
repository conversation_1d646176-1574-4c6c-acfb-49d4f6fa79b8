<template>
  <cm-container>
    <div class="alarm-settings-setting-box">
      <div class="alarm-settings-setting">
        <div class="alarm-settings-setting-content">
          <div class="alarm-settings-setting-content-header">
            <div class="alarm-settings-setting-content-header-title-box">
              <el-icon color="#2d55eb" size="18">
                <Document />
              </el-icon>
              <span class="alarm-settings-setting-content-header-title"> 专业信息</span>
            </div>
          </div>
          <div class="alarm-settings-setting-content-table">
            <el-table :data="zyTableData" style="width: 100%">
              <el-table-column prop="nj" label="年级" align="center" width="150" />
              <el-table-column prop="zyssyxbmc" label="院系" align="center" />
              <el-table-column prop="zymc" label="专业" align="center" width="200" />
            </el-table>
          </div>
        </div>
      </div>
      <!-- 基础设置 -->
      <div class="alarm-settings-setting">
        <div class="alarm-settings-setting-header">
          <div class="alarm-settings-setting-header-left">
            <img
              class="alarm-settings-setting-header-icon"
              src="/img/alarmAnalyse/card-icon.png"
              alt=""
            />
            <span class="alarm-settings-setting-header-title">基础设置</span>
          </div>
        </div>
        <div class="alarm-settings-setting-content">
          <div class="alarm-settings-setting-content-header">
            <div class="alarm-settings-setting-content-header-title-box">
              <el-icon color="#2d55eb" size="18">
                <Document />
              </el-icon>
              <span class="alarm-settings-setting-content-header-title"> 预警时间</span>
            </div>
            <el-button type="primary" @click="dialogYjsjVisible = true">添加日期</el-button>
          </div>
          <div class="alarm-settings-setting-content-table">
            <el-table :data="alarmListDate" style="width: 100%">
              <el-table-column prop="kssj" label="开始日期" />
              <el-table-column prop="jssj" label="结束日期" />
              <el-table-column
                prop="duration"
                label="时长"
                :formatter="row => `${row.duration}天`"
              />
              <el-table-column label="操作" width="80" align="center">
                <template #default="scope">
                  <el-button type="primary" link @click="onDeleteAlarmListDate(scope.row.$index)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
      <!-- 课程设置 -->
      <div class="alarm-settings-setting">
        <div class="alarm-settings-setting-header">
          <div class="alarm-settings-setting-header-left">
            <img
              class="alarm-settings-setting-header-icon"
              src="/img/alarmAnalyse/card-icon.png"
              alt=""
            />
            <span class="alarm-settings-setting-header-title">课程设置</span>
          </div>
          <div class="alarm-settings-setting-header-right">
            <el-button type="primary" @click="dialogCjVisible = true">批量设置成绩</el-button>
          </div>
        </div>

        <div
          class="alarm-settings-setting-content"
          v-for="(item, index) in kcTableData"
          :key="item.xnmc"
        >
          <div class="alarm-settings-setting-content-header">
            <div class="alarm-settings-setting-content-header-title-box">
              <el-icon color="#2d55eb" size="18">
                <Document />
              </el-icon>
              <span class="alarm-settings-setting-content-header-title">
                {{ item.xnmc }}（{{ item.kssj }}-{{ item.jssj }}）</span
              >
            </div>
          </div>
          <div class="alarm-settings-setting-content-table">
            <el-table
              :data="item.kcDTOList || []"
              style="width: 100%"
              ref="tableRef"
              border
              show-overflow-tooltip
              @selection-change="
                val => {
                  handleSelectionChange(val, index);
                }
              "
            >
              <el-table-column type="selection" width="55" align="center" />
              <el-table-column prop="xqmc" label="开课学年/学期" width="200" />
              <el-table-column prop="nj" label="年级" min-width="150" />
              <el-table-column prop="dwmc" label="院系" width="200" />
              <el-table-column prop="zymc" label="专业" width="200" />
              <el-table-column prop="kcxzmc" label="课程性质" width="200" />
              <el-table-column prop="kcsxm" label="课程属性" width="200" />
              <el-table-column prop="kch" label="课程号" width="200" />
              <el-table-column prop="kcmc" label="课程" width="200" />
              <el-table-column prop="sfbg" label="是否必过" width="200" />
              <el-table-column prop="cjyq" label="成绩要求（＞＝）" width="150" />
              <el-table-column label="操作" width="150" align="center" fixed="right">
                <template #default="scope">
                  <el-button
                    type="primary"
                    link
                    v-perms="'alarmSettings:setting'"
                    @click="onSettingKc(scope.row)"
                    >设置课程</el-button
                  >
                  <el-button type="primary" link @click="onClearSettings(scope.row)"
                    >清空</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
    <div class="alarm-settings-setting-box-footer">
      <el-button @click="onCancel" v-loading="saveLoading">取 消</el-button>
      <el-button type="primary" @click="onSave" v-loading="saveLoading">保存</el-button>
    </div>
    <cm-dialog
      v-model="dialogYjsjVisible"
      title="添加预警时间"
      width="750"
      class="alarm-date-dialog"
    >
      <div class="alarm-date-dialog-content">
        <CalendarPanel
          @close="dialogYjsjVisible = false"
          ref="calendarPanelRef"
          :isBatch="false"
          @confirm="onConfirmDate"
        />
      </div>
      <template #footer>
        <el-button @click="onCloseCalendar">取消</el-button>
        <el-button @click="clearSelection">清空选择</el-button>
        <el-button type="primary" @click="onConfirmCalendar"> 确 定 </el-button>
      </template>
    </cm-dialog>

    <cm-dialog
      v-model="dialogCjVisible"
      title="设置"
      width="500"
      class="cm-dialog"
      @close="ruleForm.cjyq = null"
    >
      <div style="padding: 20px">
        <el-form ref="ruleFormCjRef" :model="ruleForm" :rules="rules" label-width="auto">
          <el-form-item label="成绩要求（大于等于）" prop="cjyq">
            <el-input-number
              v-model="ruleForm.cjyq"
              :min="0"
              :max="100"
              class="inp-text-left"
              :controls="false"
              placeholder="请输入成绩"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="dialogCjVisible = false">取消</el-button>
        <el-button type="primary" @click="onConfirmCj" :disabled="!ruleForm.cjyq">
          确 定
        </el-button>
      </template>
    </cm-dialog>
  </cm-container>
</template>

<script setup>
import { ElMessageBox, ElMessage } from 'element-plus';
import CmDialog from '@/components/cm-dialog/index.vue';
import CmContainer from '@/components/cm-container/main.vue';
import CalendarPanel from './components/CalendarPanel.vue';
import { useRoute, useRouter } from 'vue-router';
import {
  getYjkcDetailAPI,
  getYjsjDetailAPI,
  getZyDetailAPI,
  saveKcConfigAPI,
} from '@/api/alarmSettings';

const router = useRouter();
const route = useRoute();

const zyTableData = ref([]);
getZyDetailAPI({
  zyh: route.query.zyh,
}).then(reslut => {
  const { code, data } = reslut.data || {};
  if (code === 200) {
    zyTableData.value = [
      {
        zyssyxbmc: data.zyssyxbmc,
        zymc: data.zymc,
        nj: route.query.nj,
      },
    ];
  }
});

// 获取课程设置列表
const kcTableData = ref([]);
getYjkcDetailAPI({
  nj: route.query.nj,
  zyh: route.query.zyh,
}).then(reslut => {
  const { code, data } = reslut.data || {};
  if (code === 200) {
    kcTableData.value = data;
  }
});

// 取消
const onCancel = () => {
  router.back();
};

// 设置预警时间
const dialogYjsjVisible = ref(false);
const alarmListDate = ref([]);
const calendarPanelRef = useTemplateRef('calendarPanelRef');

if (route.query.id) {
  getYjsjDetailAPI({
    yjId: route.query.id,
  }).then(reslut => {
    const { code, data } = reslut.data || {};
    if (code === 200) {
      alarmListDate.value = data;
    }
  });
}

const onDeleteAlarmListDate = index => {
  ElMessageBox.confirm('确定删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      alarmListDate.value.splice(index, 1);
    })
    .catch(() => {});
};

// 确认选择
const onConfirmDate = data => {
  alarmListDate.value.push({
    kssj: data.start,
    jssj: data.end,
    durationText: data.durationText,
    duration: data.duration,
    nj: route.query.nj,
    zyh: route.query.zyh,
  });
};

// 确认选择
const onConfirmCalendar = () => {
  calendarPanelRef.value.confirmSelection();
};

// 清空选择
const clearSelection = () => {
  calendarPanelRef.value.clearSelection();
};

// 关闭预警时间
const onCloseCalendar = () => {
  calendarPanelRef.value.onClose();
};

//课程设置
const dialogCjVisible = ref(false);
const ruleForm = ref({
  cjyq: null,
});
const rules = ref({
  cjyq: [{ required: true, message: '请输入成绩', trigger: 'blur' }],
});
const ruleFormCjRef = useTemplateRef('ruleFormCjRef');
const tableRef = useTemplateRef('tableRef');
const onConfirmCj = () => {
  ruleFormCjRef.value.validate(valid => {
    if (valid) {
      dialogCjVisible.value = false;
      if (settingKcRow.value) {
        settingKcRow.value.cjyq = ruleForm.value.cjyq;
      } else {
        selectedRows.flat().forEach(item => {
          item.cjyq = ruleForm.value.cjyq;
        });
        selectedRows = [];
        tableRef.value.forEach(item => {
          item?.clearSelection();
        });
      }
    }
  });
};

// 批量设置成绩
let selectedRows = [];
const handleSelectionChange = (item, index) => {
  selectedRows[index] = item;
};

// 设置课程
const settingKcRow = ref(void 0);
const onSettingKc = row => {
  settingKcRow.value = row;
  dialogCjVisible.value = true;
};

const onClearSettings = row => {
  ElMessageBox.confirm('确定清空吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    row.cjyq = null;
  });
};

// 保存修改的数据并提交到服务器
const saveLoading = ref(false);
const onSave = () => {
  saveLoading.value = true;
  saveKcConfigAPI({
    yjSjVOS: alarmListDate.value,
    yjKcDetailVOS: kcTableData.value,
  })
    .then(reslut => {
      const { code } = reslut.data || {};
      if (code === 200) {
        router.back();
        ElMessage.success('保存成功');
      }
    })
    .finally(() => {
      saveLoading.value = false;
    });
};
</script>

<style lang="scss" scoped>
.alarm-settings-setting-box {
  height: calc(100% - 70px);
  overflow-y: auto;

  .alarm-settings-setting {
    width: 100%;
    background: #fff;
    border-radius: 4px;
    border: 2px solid #ffffff;
    backdrop-filter: blur(10px);
    box-sizing: border-box;
    box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;

    .alarm-settings-setting-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;

      .alarm-settings-setting-header-left {
        display: flex;
        align-items: center;
        gap: 14px;

        .alarm-settings-setting-header-icon {
          width: 24px;
          height: 24px;
        }

        .alarm-settings-setting-header-title {
          font-size: 16px;
          font-weight: 600;
          color: #333333;
        }
      }

      .alarm-settings-setting-header-right {
        display: flex;
        align-items: center;
      }
    }

    .alarm-settings-setting-content {
      background: #fcfcfc;
      border-radius: 4px;
      padding: 20px;
      box-sizing: border-box;
      height: 100%;
      box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .alarm-settings-setting-content-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .alarm-settings-setting-content-header-title {
          font-family: AppleSystemUIFont;
          font-size: 13px;
          color: #333333;
        }

        .alarm-settings-setting-content-header-title-box {
          display: flex;
          align-items: center;
          gap: 6px;
        }
      }

      .alarm-settings-setting-content-table {
        margin-top: 20px;
      }
    }
  }

  .mt-20 {
    margin-bottom: 20px;
  }
}

.alarm-settings-setting-box-footer {
  margin-top: 10px;
  height: 60px;
  background: #fff;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  box-sizing: border-box;
}

.alarm-date-dialog {
  .alarm-date-dialog-content {
    padding: 20px;
  }
}
</style>
