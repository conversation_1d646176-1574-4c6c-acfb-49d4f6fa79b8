<template>
  <cm-container>
    <div class="warning-info">
         <!-- 温馨提示 -->
      <div class="warning-tip">
        【温馨提示】学生毕业的最终解释权归教务处所有。
      </div>
      <!-- 搜索框部分 -->
      <div class="search-box">
        <el-select v-model="searchParams.grade"  placeholder="请选择年级">
              <el-option label="2022" value="2022"></el-option>
              <!-- 其他年级选项 -->
            </el-select>
             <el-select v-model="searchParams.college"  placeholder="请选择院系">
              <el-option label="通信工程学院" value="通信工程学院"></el-option>
              <!-- 其他院系选项 -->
            </el-select>
              <el-select v-model="searchParams.major" placeholder="请选择专业">
              <el-option label="通信工程（本科）" value="通信工程（本科）"></el-option>
              <!-- 其他专业选项 -->
            </el-select>
              <el-select v-model="searchParams.class" placeholder="请选择班级">
              <el-option label="2022通信工程（本科）" value="2022通信工程（本科）"></el-option>
              <!-- 其他班级选项 -->
            </el-select>
             <el-input v-model="searchParams.name" placeholder="请输入姓名"></el-input>
               <el-select v-model="searchParams.processStatus" placeholder="请选择处理状态">
              <el-option label="未处理" value="未处理"></el-option>
              <el-option label="已处理" value="已处理"></el-option>
            </el-select>
            <el-select v-model="searchParams.latestStatus" placeholder="请选择最新课程状态">
              <el-option label="不合格" value="不合格"></el-option>
              <el-option label="合格" value="合格"></el-option>
            </el-select>
         <div class="search-box-btn">
          <el-button type="primary" @click="onSearch">搜索</el-button>
          <el-button @click="onClear">清空</el-button>
        </div>
      </div>
      <!-- 标签页部分 -->
      <div class="warning-info-tabs" >
        <el-radio-group v-model="radio" @change="changelist" size="medium">
          <el-radio-button label="1">课程成绩预警</el-radio-button>
          <el-radio-button label="2">顶岗实习成绩预警</el-radio-button>
          <el-radio-button label="3">毕业设计成绩预警</el-radio-button>
        </el-radio-group>
      </div>
      <!-- 表格部分 -->
       <div class="warning-info-table" ref="tableRef">
        <el-table :data="tableData"  style="width: 100%" :height="tableHeight">
        <el-table-column v-for="column in columns" :key="column.prop" :prop="column.prop" :label="column.label" :width="column.width">
          <template #default="scope">
            <!-- 自定义列内容 -->
             <span v-if="column.prop === 'processStatus' || column.prop === 'latestStatus'">
                <span :style="{ color: getStatusColor(scope.row[column.prop]) }">
                  <!-- {{ scope.row[column.prop] }} -->
                </span>
              </span>
              <!-- 操作列的具体内容 -->
            <span v-if="column.prop === 'operation'">
              <el-button size="mini" type="text" @click="handle(scope.$index, scope.row)">处理</el-button>
              <!-- <el-button size="mini" type="text" @click="topUp(scope.$index, scope.row)">置顶</el-button> -->
              <el-button size="mini" type="text" @click="topUp(scope.$index, scope.row)">取消置顶</el-button>
              <!-- <el-button size="mini" type="text" @click="details(scope.$index, scope.row)">查看</el-button> -->
            </span>
            <span v-else>{{ scope.row[column.prop] }}</span>
          </template>
        </el-table-column>
      </el-table>
       <div class="cm-pagination-right">
          <el-pagination v-model:current-page="pageParams.current"
            v-model:page-size="pageParams.size" background
            layout="total, sizes, prev, pager, next, jumper"
            :total="pageParams.total" @change="onLoad" />
        </div>
       </div>
      
    </div>
  </cm-container>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router'
import CmContainer from '@/components/cm-container/main.vue';
import { useResizeObserver } from '@/hooks/useResizeObserver'
// 搜索参数
const searchParams = ref({
  grade: '',
  college: '',
  major: '',
  class: '',
  name: '',
  processStatus: '',
  latestStatus: ''
});

//分页参数
const pageParams = ref({
  total: 0,
  current: 1,
  size: 10,
})
const tableData = ref([]);
const columns = ref([]);
// 表格列定义 - 课程成绩预警
const columns1 = [
  { prop: 'seq', label: '序号', width: '55' },
  { prop: 'alertTime', label: '预警时间',width: '100'  },
  { prop: 'grade', label: '年级', width: '60'  },
  { prop: 'college', label: '学院',  },
  { prop: 'major', label: '专业',  },
  { prop: 'class', label: '班级',  },
  { prop: 'studentId', label: '学号',  },
  { prop: 'name', label: '姓名',  },
  { prop: 'designScore1', label: '开课学年/学期', },
  { prop: 'designScore2', label: '课程性质',  },
  { prop: 'designScore3', label: '课程属性',  },
  { prop: 'designScore4', label: '课程号',},
  { prop: 'designScore5', label: '课程', },
  { prop: 'designScore', label: '实得学分',  },
  { prop: 'examScore', label: '实得成绩',  },
  { prop: 'scoreType', label: '成绩类型',   },
  { prop: 'examScore1', label: '是否必过', },
  { prop: 'processStatus', label: '处理状态',  },
  { prop: 'processTime', label: '处理时间', },
  { prop: 'latestStatus', label: '最新课程状态',  },
  { prop: 'operation', label: '操作', width: '140' } // 操作列
];

// 表格数据 - 课程成绩预警
const tableData1 = ref([
  {
    seq: '1',
    alertTime: '2022-12-12',
    grade: '2022',
    college: '通信工程学院',
    major: '通信工程（本科）',
    class: '2022通信工程（本科）',
    studentId: '123123999',
    name: '张三',
    designScore: '50',
    scoreType: '首修',
    processStatus: '未处理',
    processTime: '-',
    latestStatus: '不合格',
    operation: '处理 查看'
  },
]);
// 表格列定义 - 顶岗实习成绩预警
const columns2 = [
  { prop: 'seq', label: '序号', width: '55' },
  { prop: 'alertTime', label: '预警时间', width: '120' },
  { prop: 'grade', label: '年级', width: '80' },
  { prop: 'college', label: '学院',  },
  { prop: 'major', label: '专业',  },
  { prop: 'class', label: '班级', },
  { prop: 'studentId', label: '学号', },
  { prop: 'name', label: '姓名', width: '100' },
  { prop: 'designScore', label: '顶岗实习成绩', width: '120' },
  { prop: 'scoreType', label: '成绩类型', width: '100' },
  { prop: 'processStatus', label: '处理状态', width: '100' },
  { prop: 'processTime', label: '处理时间', width: '120' },
  { prop: 'latestStatus', label: '最新课程状态', width: '120' },
  { prop: 'operation', label: '操作', width: '140' } // 操作列
];

// 表格数据 - 顶岗实习成绩预警
const tableData2 = ref([
  {
    seq: '1',
    alertTime: '2022-12-12',
    grade: '2022',
    college: '通信工程学院',
    major: '通信工程（本科）',
    class: '2022通信工程（本科）',
    studentId: '123123',
    name: '张三',
    designScore: '50',
    scoreType: '首修',
    processStatus: '未处理',
    processTime: '-',
    latestStatus: '不合格',
    operation: '处理 查看'
  },
]);
// 表格列定义 - 毕业设计成绩预警
const columns3 = [
  { prop: 'seq', label: '序号', width: '55' },
  { prop: 'alertTime', label: '预警时间',  },
  { prop: 'grade', label: '年级', width: '80' },
  { prop: 'college', label: '学院', },
  { prop: 'major', label: '专业',  },
  { prop: 'class', label: '班级', },
  { prop: 'studentId', label: '学号',  },
  { prop: 'name', label: '姓名', width: '100' },
  { prop: 'designScore', label: '毕业设计成绩', width: '120' },
  { prop: 'scoreType', label: '成绩类型', width: '100' },
  { prop: 'processStatus', label: '处理状态', width: '100' },
  { prop: 'processTime', label: '处理时间', width: '120' },
  { prop: 'latestStatus', label: '最新课程状态', width: '120' },
  { prop: 'operation', label: '操作', width: '140' } // 操作列
];

// / 表格数据 - 毕业设计成绩预警
const tableData3 = ref([
  {
    seq: '1',
    alertTime: '2022-12-12',
    grade: '2022',
    college: '通信工程学院',
    major: '通信工程（本科）',
    class: '2022通信工程（本科）',
    studentId: '123123',
    name: '张三',
    designScore: '50',
    scoreType: '首修',
    processStatus: '未处理',
    processTime: '-',
    latestStatus: '不合格',
    operation: '处理 查看'
  },
]);

// 当前选中的标签
const radio = ref('1');
 // 表格引用
const tableRef = useTemplateRef('tableRef')
 // 表格高度
const tableHeight = ref(0)
//初始化
onMounted(() => {
  useResizeObserver(tableRef, entries => {
    const entry = entries[0]
    const { height: _h } = entry.contentRect
    tableHeight.value = _h - 54
  })
  console.log(tableData,'123');
  changelist()
})

// 切换标签时的处理函数
const changelist = () => {
  console.log(radio.value);
  if (radio.value == 1) { 
      columns.value = columns1
      tableData.value = tableData1.value
  }else if (radio.value == 2) { 
      columns.value = columns2
      tableData.value = tableData2.value
  }else if (radio.value == 3) { 
      columns.value = columns3
      tableData.value = tableData3.value
  }
};
// 搜索函数
const onSearch = () => {
  console.log(searchParams.value);
};

// 清空函数
const onClear = () => {
  searchParams.value = {};
};
const router = useRouter();
// 处理
const handle = (index, row) => {
  console.log(index, row);
   router.push({
    path: '/warningInfo/details',
    query: {
      id: row.id,
    },
  })
};
// 置顶
const topUp = (index, row) => {
  console.log(index, row);
};
// 查看
const details = (index, row) => {
  console.log(index, row);
};
// 状态颜色映射函数
const getStatusColor = (status) => {
  if (status === '不合格' || status === '未处理') {
    return 'red';
  } else if (status === '已处理' || status === '合格') {
    return 'green';
  }
  return ''; // 默认颜色
};
//加载数据
const onLoad = () => {
  console.log(pageParams.value)
}
</script>

<style lang="scss" scoped>
.warning-info {
  width: 100%;
  height: 100%;
  background-color: #fff;
  
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 12px;
    .warning-tip {
    background-color: #ffecec;
    color: #f56c6c;
    padding: 8px;
  }
  .search-box{
    padding: 0 10px;
    margin-top: 0px;
  }
  .warning-info-tabs{
       padding: 0 10px;
  }
   .warning-info-table {
    padding: 0 10px;
    flex: 1;
  }
}
</style>