
const global = {
  state: {
    globalList: [],
  },
  actions: {
    // 设置字典
    setGlobal({ state }, { key, value }) {
      if (key !== null && key !== '') {
        state.globalList.push({ key, value });
      }
    },

    // 删除字典
    removeGlobal({ state }, _key) {
      let removed = false;
      try {
        for (let i = 0; i < state.globalList.length; i++) {
          if (state.globalList[i].key === _key) {
            state.globalList.splice(i, 1);
            removed = true;
            break; // 找到并移除后直接退出循环
          }
        }
      } catch (e) {
        removed = false;
      }
      return removed;
    },

    // 清空字典
    cleanGlobal({ state }) {
      state.globalList = []; // 直接赋空数组即可清空字典
    },
  },
  mutations: {},
}

export default global;