<template>
  <cm-container @onLoad="onLoad">
    <div
      class="alarm-settings"
      element-loading-text="加载中..."
      v-loading="loading"
      :element-loading-svg="svg"
      element-loading-background="rgba(255, 255, 255, 0.5)"
      element-loading-svg-view-box="-10, -10, 50, 50"
    >
      <div class="alarm-settings-search">
        <el-select
          style="width: 240px"
          v-model="searchParams.nj"
          placeholder="请选择年级"
          clearable
        >
          <el-option
            v-for="item in njList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-select
          style="width: 240px"
          v-model="searchParams.yxbh"
          placeholder="请选择院系"
          clearable
        >
          <el-option
            v-for="item in yxList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-select
          style="width: 240px"
          v-model="searchParams.zyh"
          placeholder="请选择专业"
          clearable
        >
          <el-option
            v-for="item in zyList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <div class="alarm-settings-search-button">
          <el-button type="primary" @click="onSearch"> 搜索 </el-button>
          <el-button @click="onClear">清空</el-button>
        </div>
      </div>
      <div class="alarm-settings-operations">
        <el-button
          type="primary"
          size="small"
          @click="onBatchBysj('bdsj')"
          :disabled="selectedRows.length === 0"
          >批量设置毕业设计</el-button
        >
        <el-button
          type="primary"
          size="small"
          @click="onBatchBysj('bdsjAll')"
          :disabled="tableData.length === 0"
          >设置全部毕业设计</el-button
        >
        <el-button
          type="primary"
          size="small"
          @click="onBatchDgsx('dgsx')"
          :disabled="selectedRows.length === 0"
          >批量设置顶岗实习</el-button
        >
        <el-button
          type="primary"
          size="small"
          @click="onBatchDgsx('dgsxAll')"
          :disabled="tableData.length === 0"
          >设置全部顶岗实习</el-button
        >
        <el-button
          type="primary"
          size="small"
          @click="onBatchYjsj('yjsj')"
          :disabled="selectedRows.length === 0"
          >批量设置预警时间</el-button
        >
        <el-button
          type="primary"
          size="small"
          @click="onBatchYjsj('yjsjAll')"
          :disabled="tableData.length === 0"
          >设置全部预警时间</el-button
        >
      </div>
      <div class="alarm-settings-table" ref="tableRef">
        <el-table
          :data="tableData"
          style="width: 100%"
          :height="tableHeight"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column prop="nj" label="年级" />
          <el-table-column prop="yxmc" label="院系" />
          <el-table-column prop="zymc" label="专业" />
          <el-table-column prop="kcyq" label="课程要求" width="100">
            <template #default="scope">
              <el-tag v-if="scope.row.kcyq === '1'" type="success">已设置</el-tag>
              <el-tag v-else type="danger">未设置</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="bysjfs" label="毕业设计" width="100" />
          <el-table-column prop="dgsxfs" label="顶岗实习" width="100" />
          <el-table-column label="操作" width="150" align="center">
            <template #default="scope">
              <el-button
                type="primary"
                link
                v-perms="'alarmSettings:setting'"
                @click="onJumpSetting(scope.row)"
                >设置课程</el-button
              >
              <el-button type="primary" link @click="onClearSettings(scope.row)">清空</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="cm-pagination-right">
          <el-pagination
            v-model:current-page="pageParams.current"
            v-model:page-size="pageParams.size"
            background
            layout="total, sizes, prev, pager, next, jumper"
            :total="pageParams.total"
            @change="onLoad"
          />
        </div>
      </div>
    </div>
    <cm-dialog
      v-model="dialogBysjVisible"
      title="设置"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="ruleForm.bysjfs = void 0"
      class="cm-dialog"
    >
      <template #default>
        <div class="alarm-settings-dialog-content">
          <el-form ref="ruleFormBysjRef" :model="ruleForm" :rules="rules" label-width="auto">
            <el-form-item label="毕业设计成绩（大于等于）" prop="bysjfs">
              <el-input-number
                v-model="ruleForm.bysjfs"
                :min="0"
                :max="100"
                class="inp-text-left"
                :controls="false"
                placeholder="请输入毕业设计成绩（大于等于）"
              />
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #footer>
        <el-button @click="dialogBysjVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="onConfirmBysj"
          :disabled="!ruleForm.bysjfs"
          :loading="approvalBysjLoading"
        >
          确 定
        </el-button>
      </template>
    </cm-dialog>

    <cm-dialog
      v-model="dialogDgsxVisible"
      title="设置"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="ruleForm.dgsxfs = void 0"
      class="cm-dialog"
    >
      <template #default>
        <div class="alarm-settings-dialog-content">
          <el-form ref="ruleFormDgsxRef" :model="ruleForm" :rules="rules" label-width="auto">
            <el-form-item label="顶岗实习成绩（大于等于）" prop="dgsxfs">
              <el-input-number
                v-model="ruleForm.dgsxfs"
                :min="0"
                :max="100"
                class="inp-text-left"
                :controls="false"
                placeholder="请输入顶岗实习成绩（大于等于）"
              />
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #footer>
        <el-button @click="dialogDgsxVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="onConfirmDgsx"
          :disabled="!ruleForm.dgsxfs"
          :loading="approvalDgsxLoading"
        >
          确 定
        </el-button>
      </template>
    </cm-dialog>

    <cm-dialog
      v-model="dialogYjsjVisible"
      title="设置"
      width="800px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="
        calendarVisible = false;
        alarmListDate = [];
      "
      class="cm-dialog"
    >
      <template #default>
        <div class="alarm-settings-dialog-content">
          <div class="alarm-date" v-if="settingType === 'yjsj' || settingType === 'yjsjAll'">
            <div class="alarm-date-header">
              <span class="alarm-date-header-title">预警时间</span>
              <el-popover
                placement="bottom"
                :width="800"
                :visible="calendarVisible"
                trigger="click"
              >
                <template #reference>
                  <el-button type="primary" @click="showCalendar">添加日期</el-button>
                </template>
                <CalendarPanel @close="calendarVisible = false" @confirm="onConfirmDate" />
              </el-popover>
            </div>
            <div class="alarm-date-table">
              <el-table :data="alarmListDate" style="width: 100%" height="300">
                <el-table-column prop="kssj" label="开始日期" />
                <el-table-column prop="jssj" label="结束日期" />
                <el-table-column prop="durationText" label="时长" />
                <el-table-column label="操作" width="80" align="center">
                  <template #default="scope">
                    <el-button type="primary" link @click="onDeleteAlarmListDate(scope.row.$index)"
                      >删除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </template>
      <template #footer>
        <el-button @click="dialogYjsjVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="onConfirmYjsjAlarmListDate"
          :disabled="alarmListDate.length === 0"
          :loading="approvalYjsjLoading"
        >
          确 定
        </el-button>
      </template>
    </cm-dialog>
  </cm-container>
</template>

<script setup>
import { useRouter } from 'vue-router';
import { ElMessageBox } from 'element-plus';
import CmContainer from '@/components/cm-container/main.vue';
import { useResizeObserver } from '@/hooks/useResizeObserver';
import CmDialog from '@/components/cm-dialog/index.vue';
import CalendarPanel from './components/CalendarPanel.vue';
import useBusinessData from '@/hooks/useBusinessData';
import {
  getListAPI,
  saveYjBatchByCheckAPI,
  saveYjBatchAPI,
  saveYjSjBatchByCheckAPI,
  saveYjSjBatchAPI,
  clearSettingAPI,
} from '@/api/alarmSettings';

const loading = ref(true);
const svg = `
        <path class="path" d="
          M 30 15
          L 28 17
          M 25.61 25.61
          A 15 15, 0, 0, 1, 15 30
          A 15 15, 0, 1, 1, 27.99 7.5
          L 15 15
        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
      `;

const ruleForm = reactive({});
const alarmListDate = ref([]);
const settingType = ref('');
const rules = reactive({
  bysjfs: [{ required: true, message: '请输入毕业设计成绩', trigger: 'blur' }],
  dgsxfs: [{ required: true, message: '请输入顶岗实习成绩', trigger: 'blur' }],
});

/**
 * 表格引用
 * @type {Ref<HTMLElement>}
 */
const tableRef = useTemplateRef('tableRef');

/**
 * 表格高度
 * @type {Ref<number>}
 */
const tableHeight = ref(0);

/**
 * 分页参数
 * @type {Ref<Object>}
 * @property {number} total 总条数
 * @property {number} current 当前页
 * @property {number} size 每页条数
 */
const pageParams = ref({
  total: 0,
  current: 1,
  size: 10,
});

/**
 * 搜索参数
 * @type {Ref<Object>}
 * @property {string} userName 申请人
 * @property {string} userCode 工号
 * @property {string} deptName 所属部门
 */
const searchParams = ref({
  nj: '',
  yxbh: '',
  zyh: '',
});

const { njList, yxList, zyList } = useBusinessData('nj', 'yx', 'zy');

/**
 * 初始化
 */
onMounted(() => {
  useResizeObserver(tableRef, entries => {
    const entry = entries[0];
    const { height: _h } = entry.contentRect;
    tableHeight.value = _h - 54;
  });
});

const onClearSettings = row => {
  ElMessageBox.confirm('确定清空该条数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      clearSettingAPI({ id: row.id, nj: row.nj, zybm: row.zyh, kcyq: 0 }).then(reslut => {
        const { code } = reslut.data || {};
        if (code === 200) {
          onLoad();
        }
      });
    })
    .catch(() => {});
};

/**
 * 搜索
 */
const onSearch = () => {
  pageParams.value.current = 1;
  onLoad();
};

/**
 * 清空搜索
 */
const onClear = () => {
  searchParams.value = {
    nj: '',
    yxbh: '',
    zyh: '',
  };
  pageParams.value.current = 1;
  onLoad();
};

/**
 * 加载数据
 */
const tableData = ref([]);
const onLoad = () => {
  loading.value = true;
  getListAPI(pageParams.value.current, pageParams.value.size, searchParams.value)
    .then(result => {
      const { data, code } = result.data || {};
      if (code === 200) {
        tableData.value = data.records;
        pageParams.value.total = data.total;
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

const router = useRouter();

const onJumpSetting = row => {
  router.push({
    path: '/alarmSettings/setting',
    query: {
      id: row.id,
      nj: row.nj,
      zyh: row.zyh,
    },
  });
};

const selectedRows = ref([]);
const handleSelectionChange = rows => {
  selectedRows.value = rows.map(item => {
    return {
      id: item.id,
      nj: item.nj,
      zybm: item.zyh,
    };
  });
};

//毕业设计设置
const ruleFormBysjRef = useTemplateRef('ruleFormBysjRef');
const dialogBysjVisible = ref(false);
const approvalBysjLoading = ref(false);
const onBatchBysj = type => {
  ruleForm.dgsxfs = void 0;
  settingType.value = type;
  dialogBysjVisible.value = true;
};
const onConfirmBysj = () => {
  ruleFormBysjRef.value.validate(valid => {
    if (valid) {
      approvalBysjLoading.value = true;
      if (settingType.value === 'bdsj') {
        saveYjBatchByCheck(dialogBysjVisible, approvalBysjLoading);
      } else if (settingType.value === 'bdsjAll') {
        saveYjBatch(dialogBysjVisible, approvalBysjLoading);
      }
    }
  });
};

//批量设置毕业设计或顶岗实习（勾选）
const saveYjBatchByCheck = (dialogVisible, loading) => {
  loading.value = true;
  saveYjBatchByCheckAPI({ ...ruleForm, checkedList: selectedRows.value })
    .then(result => {
      const { code } = result.data || {};
      if (code === 200) {
        dialogVisible.value = false;
        selectedRows.value = [];
        onLoad();
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

//批量设置毕业设计或顶岗实习（全部）
const saveYjBatch = (dialogVisible, loading) => {
  loading.value = true;
  debugger;
  saveYjBatchAPI({ ...ruleForm, ...searchParams.value })
    .then(result => {
      const { code } = result.data || {};
      if (code === 200) {
        dialogVisible.value = false;
        selectedRows.value = [];
        onLoad();
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

//顶岗实习设置
const ruleFormDgsxRef = useTemplateRef('ruleFormDgsxRef');
const dialogDgsxVisible = ref(false);
const approvalDgsxLoading = ref(false);
const onBatchDgsx = type => {
  ruleForm.bysjfs = void 0;
  settingType.value = type;
  dialogDgsxVisible.value = true;
};
// 确认设置顶岗实习
const onConfirmDgsx = () => {
  ruleFormDgsxRef.value.validate(valid => {
    if (valid) {
      if (settingType.value === 'dgsx') {
        saveYjBatchByCheck(dialogDgsxVisible, approvalDgsxLoading);
      } else if (settingType.value === 'dgsxAll') {
        saveYjBatch(dialogDgsxVisible, approvalDgsxLoading);
      }
    }
  });
};

//预警时间设置
const dialogYjsjVisible = ref(false);
const approvalYjsjLoading = ref(false);
const calendarVisible = ref(false);
const onBatchYjsj = type => {
  settingType.value = type;
  dialogYjsjVisible.value = true;
};
const showCalendar = () => {
  calendarVisible.value = !calendarVisible.value;
};

// 确认设置预警时间
const onConfirmDate = data => {
  alarmListDate.value.push({
    kssj: data.start,
    jssj: data.end,
    durationText: data.durationText,
    duration: data.duration,
  });
};

// 批量设置预警时间（勾选）
const saveYjSjBatchByCheck = (dialogVisible, loading) => {
  loading.value = true;
  saveYjSjBatchByCheckAPI({ yjSjList: alarmListDate.value, checkedList: selectedRows.value })
    .then(result => {
      const { code } = result.data || {};
      if (code === 200) {
        dialogVisible.value = false;
        selectedRows.value = [];
        onLoad();
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 批量设置预警时间（全部）
const saveYjSjBatch = (dialogVisible, loading) => {
  loading.value = true;
  saveYjSjBatchAPI({ yjSjList: alarmListDate.value, ...searchParams.value })
    .then(result => {
      const { code } = result.data || {};
      if (code === 200) {
        dialogVisible.value = false;
        onLoad();
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

/**
 * 清空设置
 * @param {Object} row 行数据
 */
const onDeleteAlarmListDate = index => {
  ElMessageBox.confirm('确定删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      alarmListDate.value.splice(index, 1);
    })
    .catch(() => {});
};

// 提交设置预警时间
const onConfirmYjsjAlarmListDate = () => {
  approvalYjsjLoading.value = true;
  if (settingType.value === 'yjsj') {
    saveYjSjBatchByCheck(dialogYjsjVisible, approvalYjsjLoading);
  } else if (settingType.value === 'yjsjAll') {
    saveYjSjBatch(dialogYjsjVisible, approvalYjsjLoading);
  }
};
</script>

<style lang="scss" scoped>
.alarm-settings {
  width: 100%;
  height: 100%;
  background-color: #fff;
  padding: 0 10px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 12px;

  .alarm-settings-search {
    margin-top: 12px;
    display: flex;
    align-items: center;
    gap: 10px;

    .alarm-settings-search-button {
      display: flex;
      align-items: center;
    }
  }

  .alarm-settings-operations {
    display: flex;
    align-items: center;
  }

  .alarm-settings-table {
    flex: 1;
  }
}

.cm-dialog {
  .alarm-settings-dialog-content {
    padding: 20px;
  }

  .alarm-date {
    background: #f9faff;
    border: 1px solid #ffffff;
    backdrop-filter: blur(10px);
    padding: 10px;
    box-sizing: border-box;

    .alarm-date-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .alarm-date-header-title {
        font-family: AppleSystemUIFont;
        font-size: 16px;
        color: #333333;
      }
    }

    .alarm-date-table {
      margin-top: 10px;
    }
  }
}
</style>
