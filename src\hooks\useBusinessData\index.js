import { ref, toRefs } from 'vue';
import store from '@/store';
import { getYxListAPI, getZyListAPI, getNjListAPI } from '@/api/common';

const apis = {
  yx: { api: getYxListAPI, name: '院系', key: 'yxList', label:'name',value:'code' },
  zy: { api: getZyListAPI, name: '专业', key: 'zyList', label:'zymc',value:'zyh' },
  nj: { api: getNjListAPI, name: '年级', key: 'njList', label:'ssnj',value:'ssnj' }
};

/**
 * 获取业务数据
 */
const useBusinessData = (...args) => {
  const res = ref({});
  return (() => {
    args.forEach(async dictType => {
      const apiInfo = apis[dictType];
      if (!apiInfo) {
        throw new Error(`“${dictType}”API接口不存在`);
      } else {
        res.value[apiInfo.key] = [];
        const dicts = store.getters.getGlobal(apiInfo.key);
        if (dicts) {
          res.value[apiInfo.key] = dicts;
        } else {
          apiInfo.api({}).then(resp => {
            const { code, data } = resp.data;
            if (code === 200) {
              res.value[apiInfo.key] = apiInfo.format
                ? data.map(apiInfo.format)
                : data.map(d => ({
                    label: Reflect.get(d, apiInfo.label) || d.name,
                    value: Reflect.get(d, apiInfo.value) || d.code,
                  }));
              store.dispatch('setGlobal', { key: apiInfo.key, value: res.value[apiInfo.key] });
            }
          });
        }
      }
    });
    return toRefs(res.value);
  })();
};
export default useBusinessData;
