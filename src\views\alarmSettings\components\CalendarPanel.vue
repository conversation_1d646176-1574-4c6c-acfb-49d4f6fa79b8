<template>
  <div class="calendar-panel">
    <!-- 双面板布局 -->
    <div class="dual-panel-container">
      <!-- 左侧面板 - 起始日期 -->
      <div class="panel-section">
        <div class="panel-header">
          <div class="header-title">
            <span class="header-title-text">开始日期</span>
            <el-select v-model="startMonth"
              placeholder="选择月份"
              @change="onStartMonthChange">
              <el-option
                v-for="month in months"
                :key="month.value"
                :label="month.label"
                :value="month.value" />
            </el-select>
          </div>

        </div>

        <!-- 左侧日历网格 -->
        <div class="calendar-grid">
          <!-- 日期网格 -->
          <div class="dates">
            <div
              v-for="date in startMonthDates"
              :key="date"
              :class="['date-cell', 'start-panel', getDateCellClass(startMonth, date)]"
              @click="onStartDateClick(date)">
              {{ date }}
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧面板 - 结束日期 -->
      <div class="panel-section">
        <div class="panel-header">
          <div class="header-title">
            <span class="header-title-text">结束日期</span>
            <el-select v-model="endMonth" placeholder="选择月份"
              @change="onEndMonthChange">
              <el-option
                v-for="month in months"
                :key="month.value"
                :label="month.label"
                :value="month.value" />
            </el-select>
          </div>
        </div>

        <!-- 右侧日历网格 -->
        <div class="calendar-grid">
          <!-- 日期网格 -->
          <div class="dates">
            <div
              v-for="date in endMonthDates"
              :key="date"
              :class="['date-cell', 'end-panel', getDateCellClass(endMonth, date)]"
              @click="onEndDateClick(date)">
              {{ date }}
            </div>
          </div>
        </div>
      </div>
    </div>


    <!-- 操作按钮 -->
    <div class="actions" v-if="isBatch">
      <el-button @click="onClose">关闭</el-button>
      <el-button @click="clearSelection">清空选择</el-button>
      <el-button type="primary"
        @click="confirmSelection">确认选择</el-button>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({ start: '', end: '' }),
  },
  isBatch: {
    type: Boolean,
    default: true,
  },
})

// Emits
const emit = defineEmits(['update:modelValue', 'close', 'confirm'])

// 左右面板的月份
const startMonth = ref(new Date().getMonth() + 1)
const endMonth = ref(new Date().getMonth() + 1)

// 月份选项
const months = [
  { value: 1, label: '1月' },
  { value: 2, label: '2月' },
  { value: 3, label: '3月' },
  { value: 4, label: '4月' },
  { value: 5, label: '5月' },
  { value: 6, label: '6月' },
  { value: 7, label: '7月' },
  { value: 8, label: '8月' },
  { value: 9, label: '9月' },
  { value: 10, label: '10月' },
  { value: 11, label: '11月' },
  { value: 12, label: '12月' },
]

// 每月天数配置（2月固定29天）
const monthDays = {
  1: 31,
  2: 29,
  3: 31,
  4: 30,
  5: 31,
  6: 30,
  7: 31,
  8: 31,
  9: 30,
  10: 31,
  11: 30,
  12: 31,
}

// 选择状态
const selectionState = ref({
  start: null,
  end: null,
})

// 左侧面板日期数组
const startMonthDates = computed(() => {
  const days = monthDays[startMonth.value]
  return Array.from({ length: days }, (_, i) => i + 1)
})

// 右侧面板日期数组
const endMonthDates = computed(() => {
  const days = monthDays[endMonth.value]
  return Array.from({ length: days }, (_, i) => i + 1)
})

// 获取当前年份 默认2024 new Date().getFullYear()
const currentYear = 2024

// 格式化日期为字符串 (MM-DD格式)
const formatDate = (month, day) => {
  const monthStr = month.toString().padStart(2, '0')
  const dayStr = day.toString().padStart(2, '0')
  return `${monthStr}-${dayStr}`
}

// 解析日期字符串，并推断年份
const parseDate = dateStr => {
  if (!dateStr) return null
  const [month, day] = dateStr.split('-').map(Number)
  return { month, day, year: currentYear }
}

// 推断日期的年份（支持跨年逻辑）
const inferDateYear = (startDate, endDate) => {
  if (!startDate || !endDate) return { startDate, endDate }

  // 改进的跨年判断逻辑：
  // 通过比较结束日期和开始日期的时间先后顺序来准确判断是否为跨年选择
  // 如果结束日期在时间上早于开始日期，则识别为跨年范围

  // 创建同年的日期对象进行比较
  const startDateSameYear = new Date(currentYear, startDate.month - 1, startDate.day)
  const endDateSameYear = new Date(currentYear, endDate.month - 1, endDate.day)

  // 如果结束日期早于开始日期，说明是跨年范围
  const isCrossYear = endDateSameYear < startDateSameYear

  if (isCrossYear) {
    return {
      startDate: { ...startDate, year: currentYear },
      endDate: { ...endDate, year: currentYear + 1 },
    }
  }

  // 同年情况
  return {
    startDate: { ...startDate, year: currentYear },
    endDate: { ...endDate, year: currentYear },
  }
}

// 比较两个日期（考虑年份）
const compareDates = (date1, date2) => {
  if (!date1 || !date2) return 0

  // 直接使用当前年份进行比较，不需要调用 inferDateYear
  // 因为 inferDateYear 是用来推断起始和结束日期的年份，而不是用来比较任意两个日期
  const year1 = date1.year || currentYear
  const year2 = date2.year || currentYear

  // 比较年份
  if (year1 !== year2) {
    return year1 - year2
  }

  // 比较月份
  if (date1.month !== date2.month) {
    return date1.month - date2.month
  }

  // 比较日期
  return date1.day - date2.day
}

// 判断日期在范围中的状态（支持跨年）
const getDateStatus = (month, day) => {
  const start = parseDate(selectionState.value.start)
  const end = parseDate(selectionState.value.end)

  if (!start && !end) return 'none'

  // 检查是否是起始日期
  if (start && start.month === month && start.day === day) {
    return 'start'
  }

  // 检查是否是结束日期
  if (end && end.month === month && end.day === day) {
    return 'end'
  }

  // 检查是否在范围内（支持跨年）
  if (start && end) {
    // 推断年份
    const { startDate: inferredStart, endDate: inferredEnd } = inferDateYear(start, end)

    // 判断是否为跨年范围
    const isCrossYear = inferredEnd.year > inferredStart.year

    if (isCrossYear) {
      // 跨年情况：需要分三个时间段来判断
      // 时间段1：起始年的起始月份（从起始日期到月末）
      // 时间段2：起始年的起始月份之后到12月（完整月份）
      // 时间段3：结束年的1月到结束月份之前（完整月份）
      // 时间段4：结束年的结束月份（从月初到结束日期）

      const currentInStartYear = { month, day, year: inferredStart.year }
      const currentInEndYear = { month, day, year: inferredEnd.year }

      // 检查是否在起始年的范围内
      let isInStartYearRange = false
      if (month >= inferredStart.month) {
        if (month === inferredStart.month) {
          // 起始月份：只有大于起始日期的日期才在范围内
          isInStartYearRange = compareDates(currentInStartYear, inferredStart) > 0
        } else {
          // 起始月份之后的月份：整个月都在范围内
          isInStartYearRange = true
        }
      }

      // 检查是否在结束年的范围内
      let isInEndYearRange = false
      if (month <= inferredEnd.month) {
        if (month === inferredEnd.month) {
          // 结束月份：只有小于结束日期的日期才在范围内
          isInEndYearRange = compareDates(currentInEndYear, inferredEnd) < 0
        } else {
          // 结束月份之前的月份：整个月都在范围内
          isInEndYearRange = true
        }
      }

      if (isInStartYearRange || isInEndYearRange) {
        return 'range'
      }
    } else {
      // 同年情况：正常范围判断
      const currentWithYear = { month, day, year: inferredStart.year }
      const isAfterStart = compareDates(currentWithYear, inferredStart) > 0
      const isBeforeEnd = compareDates(currentWithYear, inferredEnd) < 0

      if (isAfterStart && isBeforeEnd) {
        return 'range'
      }
    }
  }

  return 'none'
}

// 获取日期单元格的CSS类
const getDateCellClass = (month, day) => {
  const status = getDateStatus(month, day)

  switch (status) {
    case 'start':
      return 'selected start-date'
    case 'end':
      return 'selected end-date'
    case 'range':
      return 'selected range-date'
    default:
      return ''
  }
}

// 起始日期点击处理（支持跨年，移除自动交换）
const onStartDateClick = day => {
  const clickedDate = formatDate(startMonth.value, day)
  selectionState.value.start = clickedDate

  // 不再进行自动日期交换，保持用户的原始选择
}

// 结束日期点击处理（支持跨年，移除自动交换）
const onEndDateClick = day => {
  const clickedDate = formatDate(endMonth.value, day)
  selectionState.value.end = clickedDate

  // 不再进行自动日期交换，保持用户的原始选择
}

// 月份变化处理
const onStartMonthChange = () => {
  // 起始月份变化时不清空选择，保持当前选择状态
}

const onEndMonthChange = () => {
  // 结束月份变化时不清空选择，保持当前选择状态
}

// 清空选择
const clearSelection = () => {
  selectionState.value = {
    start: null,
    end: null,
  }
  // 使用 generateModelValue 生成包含时长信息的完整数据
  emit('update:modelValue', generateModelValue('', ''))
}

// 确认选择
const confirmSelection = () => {
  if (selectionState.value.start && selectionState.value.end) {
    // 使用 generateModelValue 生成包含时长信息的完整数据
    const data = generateModelValue(
      selectionState.value.start,
      selectionState.value.end
    )
    emit('update:modelValue', data)
    emit('confirm', data)
    onClose()
    setTimeout(() => {
      clearSelection()
    }, 300)
  }
}

// 监听外部值变化
watch(
  () => props.modelValue,
  newValue => {
    if (newValue.start && newValue.end) {
      selectionState.value.start = newValue.start
      selectionState.value.end = newValue.end
    }
  },
  { deep: true, immediate: true },
)

// 自定义日期计算函数，2月统一按29天处理
const calculateDaysBetweenDates = (startDate, endDate) => {
  // 月份天数映射，2月固定为29天
  const daysInMonth = {
    1: 31, 2: 29, 3: 31, 4: 30, 5: 31, 6: 30,
    7: 31, 8: 31, 9: 30, 10: 31, 11: 30, 12: 31
  }

  // 如果是同一天，返回1
  if (startDate.year === endDate.year &&
    startDate.month === endDate.month &&
    startDate.day === endDate.day) {
    return 1
  }

  let totalDays = 0

  // 如果是同年同月
  if (startDate.year === endDate.year && startDate.month === endDate.month) {
    return endDate.day - startDate.day + 1
  }

  // 计算开始月份的剩余天数
  totalDays += daysInMonth[startDate.month] - startDate.day + 1

  // 计算中间完整月份的天数
  let currentYear = startDate.year
  let currentMonth = startDate.month + 1

  // 跨月但不跨年的情况
  if (startDate.year === endDate.year) {
    while (currentMonth < endDate.month) {
      totalDays += daysInMonth[currentMonth]
      currentMonth += 1
    }
  } else {
    // 跨年的情况
    // 先计算开始年份剩余的月份
    while (currentMonth <= 12) {
      totalDays += daysInMonth[currentMonth]
      currentMonth += 1
    }

    // 计算中间年份（如果有的话）
    currentYear += 1
    while (currentYear < endDate.year) {
      for (let month = 1; month <= 12; month++) {
        totalDays += daysInMonth[month]
      }
      currentYear += 1
    }

    // 计算结束年份的月份（不包括结束月份）
    for (let month = 1; month < endDate.month; month++) {
      totalDays += daysInMonth[month]
    }
  }

  // 计算结束月份的天数
  totalDays += endDate.day

  return totalDays
}

// 计算时长信息的核心逻辑
const calculateDurationInfo = (startStr, endStr) => {
  const start = parseDate(startStr)
  const end = parseDate(endStr)

  // 如果开始或结束日期为空，返回默认值
  if (!start || !end) {
    return {
      isValid: false,
      days: 0,
      text: '0天',
      isCrossYear: false,
      warning: null
    }
  }

  // 推断年份
  const { startDate: inferredStart, endDate: inferredEnd } = inferDateYear(start, end)

  // 使用自定义函数计算天数，确保2月按29天处理
  const daysDiff = calculateDaysBetweenDates(inferredStart, inferredEnd)

  // 判断是否跨年
  const isCrossYear = inferredEnd.year > inferredStart.year

  // 边界情况处理
  let warning = null
  if (daysDiff <= 0) {
    warning = '结束日期不能早于或等于开始日期'
  } else if (daysDiff > 365) {
    warning = '选择时长超过一年，请确认是否正确'
  }

  return {
    isValid: true,
    days: Math.max(daysDiff, 0),
    text: daysDiff > 0 ? `${daysDiff}天` : '0天',
    isCrossYear,
    warning
  }
}

// 生成完整的模型值
const generateModelValue = (start = '', end = '') => {
  const duration = calculateDurationInfo(start, end)
  return {
    start,
    end,
    duration: duration.isValid ? duration.days : 0,
    durationText: duration.text,
    isCrossYear: duration.isCrossYear
  }
}

const onClose = () => {
  emit('close')
  setTimeout(() => {
    clearSelection()
  }, 300)
}

defineExpose({
  onClose,
  clearSelection,
  confirmSelection
})
</script>

<style lang="scss" scoped>
.calendar-panel {
  max-width: 900px;
  border-radius: 16px;
}

.dual-panel-container {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
}

.panel-section {
  flex: 1;
  border-radius: 12px;
  padding: 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.08),
    0 2px 4px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.panel-section:hover {
  box-shadow:
    0 8px 20px rgba(0, 0, 0, 0.12),
    0 4px 8px rgba(0, 0, 0, 0.06);
}

.panel-header {
  width: 100%;
  margin-bottom: 10px;
}

.header-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .header-title-text {
    font-family: AppleSystemUIFont;
    font-size: 16px;
    color: #333333;
  }
}


.panel-header h3 {
  margin: 0;
  color: #1e293b;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.calendar-grid {
  margin-bottom: 0;
}

.dates {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 6px;
  padding: 8px;
  width: 100%;
  box-sizing: border-box;
}

.date-cell {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 12px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
  background-color: #ffffff;
  font-size: 15px;
  font-weight: 500;
  color: #374151;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}

.date-cell:hover {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-color: #3b82f6;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2);
}

/* 基础选中状态 */
.date-cell.selected {
  color: white;
  font-weight: 600;
  position: relative;
  transform: translateY(-1px);
}

/* 起始日期样式 - 绿色渐变 */
.date-cell.start-date {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border-color: #10b981;
  font-weight: 600;
  box-shadow:
    0 4px 12px rgba(16, 185, 129, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.date-cell.start-date:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-2px);
  box-shadow:
    0 6px 16px rgba(16, 185, 129, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* 结束日期样式 - 橙色渐变 */
.date-cell.end-date {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  border-color: #f59e0b;
  font-weight: 600;
  box-shadow:
    0 4px 12px rgba(245, 158, 11, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.date-cell.end-date:hover {
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
  transform: translateY(-2px);
  box-shadow:
    0 6px 16px rgba(245, 158, 11, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* 范围内日期样式 - 浅蓝色渐变 */
.date-cell.range-date {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1e40af;
  border-color: #93c5fd;
  font-weight: 500;
  box-shadow:
    0 2px 6px rgba(59, 130, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.date-cell.range-date:hover {
  background: linear-gradient(135deg, #bfdbfe 0%, #93c5fd 100%);
  color: #1d4ed8;
  transform: translateY(-1px);
  box-shadow:
    0 4px 10px rgba(59, 130, 246, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
}

/* 当起始日期和结束日期是同一天时 */
.date-cell.start-date.end-date {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border-color: #3b82f6;
  box-shadow:
    0 4px 12px rgba(59, 130, 246, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 左侧面板特殊样式 */
.start-panel .date-cell.start-date {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-color: #10b981;
}

.start-panel .date-cell.end-date {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border-color: #f59e0b;
}

/* 右侧面板特殊样式 */
.end-panel .date-cell.start-date {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-color: #10b981;
}

.end-panel .date-cell.end-date {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border-color: #f59e0b;
}

/* 范围连接效果 - 使用更现代的连接方式 */
.date-cell.range-date::before {
  content: '';
  position: absolute;
  top: 50%;
  left: -3px;
  right: -3px;
  height: 2px;
  background: linear-gradient(90deg, #93c5fd 0%, #bfdbfe 50%, #93c5fd 100%);
  z-index: -1;
  transform: translateY(-50%);
  border-radius: 1px;
}

/* 起始日期右侧连接 */
.date-cell.start-date::after {
  content: '';
  position: absolute;
  top: 50%;
  right: -3px;
  width: 6px;
  height: 2px;
  background: linear-gradient(90deg, rgba(16, 185, 129, 0.3) 0%, #93c5fd 100%);
  z-index: -1;
  transform: translateY(-50%);
  border-radius: 1px;
}

/* 结束日期左侧连接 */
.date-cell.end-date::before {
  content: '';
  position: absolute;
  top: 50%;
  left: -3px;
  width: 6px;
  height: 2px;
  background: linear-gradient(90deg, #93c5fd 0%, rgba(245, 158, 11, 0.3) 100%);
  z-index: -1;
  transform: translateY(-50%);
  border-radius: 1px;
}

.actions {
  display: flex;
  justify-content: center;
}



/* 月份选择器样式优化 */
.panel-header :deep(.el-select) {
  width: 120px;
}

.panel-header :deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 2px solid #e2e8f0;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.panel-header :deep(.el-input__wrapper:hover) {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

.panel-header :deep(.el-input__wrapper.is-focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 时长显示区域样式 */
.duration-display {
  margin: 20px 0;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(14, 165, 233, 0.08);
}

.duration-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.duration-label {
  font-size: 14px;
  color: #475569;
  font-weight: 500;
}

.duration-value {
  font-size: 16px;
  color: #0ea5e9;
  font-weight: 600;
  padding: 4px 12px;
  background: rgba(14, 165, 233, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(14, 165, 233, 0.2);
}

.cross-year-indicator {
  font-size: 12px;
  color: #f59e0b;
  background: rgba(245, 158, 11, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid rgba(245, 158, 11, 0.2);
  font-weight: 500;
}

.duration-warning {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: rgba(239, 68, 68, 0.05);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 6px;
  color: #dc2626;
  font-size: 13px;
}

.duration-warning .el-icon {
  font-size: 14px;
  color: #dc2626;
}
</style>
